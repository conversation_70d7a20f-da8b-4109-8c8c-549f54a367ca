import re
from PyPDF4 import Pdf<PERSON>ile<PERSON>ead<PERSON>

def extract_invoice_fields(text):
    data = {}

    # Sample patterns
    order_id_match = re.search(r'Order ID[:\s]+([A-Z0-9]+)', text)
    invoice_no_match = re.search(r'Invoice No[:\s]+([A-Z0-9/-]+)', text)
    date_match = re.search(r'Invoice Date[:\s]+([\d-]+)', text)

    if order_id_match:
        data["Order ID"] = order_id_match.group(1)
    if invoice_no_match:
        data["Invoice No"] = invoice_no_match.group(1)
    if date_match:
        data["Invoice Date"] = date_match.group(1)

    return data

# Read PDF
with open("file_3.pdf", "rb") as file:
    reader = PdfFileReader(file)
    num_pages = reader.numPages
    all_text = ""

    for i in range(num_pages):
        text = reader.getPage(i).extractText()
        all_text += text + "\n"

    # Extract fields
    invoice_data = extract_invoice_fields(all_text)
    print("📦 Extracted Data:", invoice_data)
